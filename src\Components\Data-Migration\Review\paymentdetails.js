import React, { useContext } from "react";
import styles from "./paymentdetails.module.css";
import globalStyles from "../../globalStyles.module.css";
import { useNavigate } from "react-router-dom";
import { MigrationContext } from "../Data-Migration";
import { migrationTemplate } from "../../apiService";

const data = {
  plan: "Pro-Migration",
  records: 500,
  estimatedTime: "2 hours 35 minutes",
  scheduled: "15, July, 2024 \n" +
    "4:00 PM",
  notification: "User 1 (name) ; User 2 (name)",
};

const PaymentDetails = ({liveMigrationPayload, planId}) => {
  const navigate = useNavigate();
  const { migrationState } = useContext(MigrationContext);

  return (
    <div className={styles.dFlex}>
      <div className={styles.section} style={{ width: '35%' }}>
        <div className={styles.targetGraphic}>
          <img
            src="/assets/Payment.png"
            alt="Payment Processed"
            className={styles.paymentImage}
          />
        </div>
      </div>
      <div className={styles.section} style={{ width: '65%' }}>
        <div className={styles.paymentHeaderContainer}>
          <span className={globalStyles.selectionName}>PAYMENT DETAILS</span>
          <div className={styles.dFlex} style={{ marginLeft: "auto" }}>
            <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
            <span className={globalStyles.guideName}>Can I change or upgrade my plan after payment</span>
          </div>
        </div>

        <div>
          <div className={styles.infoRow}>
            <span className={globalStyles.poppinsHeaderStyle}>Plan selected</span>
            <span className={styles.value}>{data.plan}</span>
          </div>

          <div className={styles.infoRow}>
            <span className={globalStyles.poppinsHeaderStyle}>Total number of records</span>
            <span className={styles.value}>{data.records} records</span>
          </div>

          <div className={styles.infoRow}>
            <span className={globalStyles.poppinsHeaderStyle}>Total estimated time for migration</span>
            <span className={styles.value}>{data.estimatedTime}</span>
          </div>

          <div className={styles.infoRow}>
            <span className={globalStyles.poppinsHeaderStyle}>Migration scheduled for</span>
            <div className={styles.value}>
              <span>{data.scheduled}</span>
            </div>
          </div>

          <div className={styles.infoRow}>
            <span className={globalStyles.poppinsHeaderStyle}>Sending notifications to</span>
            <span className={styles.value}>{data.notification}</span>
          </div>
        </div>

        <button className={styles.downloadInvoice} style={{ width: "95%", margin: "15px 0", marginLeft: "15px" }}>
          Download invoice
        </button>        <button
          className={globalStyles.mainButton}
          style={{width: "95%"}}
          onClick={async (event) => {
            localStorage.setItem('liveMigrationButtonClicked', true);
            // Store forceStep5 in localStorage to ensure it's preserved across navigation
            localStorage.setItem('forceStep5', 'true');            // Increment totalMigrationCount in plan metadata
            try {
              if (planId) {
                console.log("Plan ID for live migration:", planId);
                const oldPlanData = await migrationTemplate.get({ id: planId });
                const inJson = JSON.parse(oldPlanData.metadata);

                const countUpdatedMetadata = {
                  ...inJson,
                  totalMigrationCount: (inJson?.totalMigrationCount || 0) + 1,
                };
                await migrationTemplate.update(planId, {
                  metadata: JSON.stringify(countUpdatedMetadata),
                });
                console.log("Migration count incremented successfully");
              }
            } catch (error) {
              console.log("Error updating live migration count:", error);
            }

            navigate('/migration-results', {
              state: {
                sample: false,
                liveMigrationPayload: liveMigrationPayload,
                forceStep5: true
              }
            });
          }}
        >
          Schedule/Start Live Migration
        </button>
      </div>
    </div>
  );
};

export default PaymentDetails;